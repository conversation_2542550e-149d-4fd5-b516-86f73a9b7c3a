[package]
name = "colonylib"
version = "0.5.6"
edition = "2024"
authors = ["<PERSON> M<PERSON>lish"]
description = "A library implementing the Colony metadata framework on Autonomi"
repository = "https://github.com/zettawatt/colonylib"
license = "GPL-3.0-only"

[lib]
name = "colonylib"
crate-type = ["rlib"]

[profile.dev]
opt-level = 0
incremental = true

[dependencies]
ant-networking = "0.3.12"
autonomi = "0.5.0"
bip39 = { version = "2.1.0", features = ["rand"] }
blsttc = "8.0.2"
borsh = {version = "1.5.7", features = ["derive", "borsh-derive"]}
chrono = "0.4.41"
cocoon = "0.4.3"
dirs = "6.0.0"
futures = "0.3.31"
hex = "0.4.3"
k256 = { version = "0.13", features = ["ecdsa"] }
oxigraph = "0.4.11"
sha3 = "0.10"
oxjsonld = "0.1.0"
oxttl = "0.1.8"
serde = "1.0.219"
serde_json = "1.0.140"
sn_bls_ckd = "0.2.1"
sn_curv = { version = "0.10.1", default-features = false, features = ["num-bigint"] }
thiserror = "2.0.12"
tokio = "1.44.2"
tracing = "0.1.41"

[dev-dependencies]
tempfile = "3.20.0"
tokio-test = "0.4"
ruint = "1.12.3"
tracing-subscriber = "0.3.18"
rand = "0.8"
