use colonylib::{DataStore, KeyStore};
use std::fs;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Active Wallet Demo");
    println!("==================");

    // Create a temporary directory for this demo
    let temp_dir = tempfile::TempDir::new()?;
    let data_dir = temp_dir.path().join("data");
    let pods_dir = temp_dir.path().join("pods");
    let downloads_dir = temp_dir.path().join("downloads");

    // Create DataStore and KeyStore
    let data_store = DataStore::from_paths(data_dir.clone(), pods_dir, downloads_dir)?;
    let mut key_store = KeyStore::from_mnemonic(
        "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about",
    )?;

    println!("\n1. Adding wallet keys to the key store:");

    // Add some wallet keys (32 bytes = 64 hex characters)
    let wallet1_key = "ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80";
    let wallet2_key = "59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d";

    key_store.add_wallet_key("main", wallet1_key)?;
    key_store.add_wallet_key("backup", wallet2_key)?;

    println!("   Added 'main' wallet");
    println!("   Added 'backup' wallet");

    println!("\n2. Setting active wallet:");

    // Set active wallet using KeyStore
    let (active_name, active_address) = key_store.set_active_wallet("main")?;
    println!("   Set active wallet: {active_name} -> {active_address}");

    // Persist to DataStore
    data_store.set_active_wallet(&active_name, &active_address)?;
    println!("   Persisted to active_wallet.json");

    println!("\n3. Verifying active_wallet.json file:");

    // Check the file was created
    let active_wallet_path = data_dir.join("active_wallet.json");
    if active_wallet_path.exists() {
        let file_contents = fs::read_to_string(&active_wallet_path)?;
        println!("   File contents: {file_contents}");

        // Parse and verify JSON structure
        let json_value: serde_json::Value = serde_json::from_str(&file_contents)?;
        println!("   JSON structure is valid ✓");
        println!("   Name field: {:?}", json_value.get("name"));
        println!("   Address field: {:?}", json_value.get("address"));
    } else {
        println!("   ERROR: active_wallet.json file was not created!");
    }

    println!("\n4. Reading active wallet from DataStore:");

    // Read back from DataStore
    let (retrieved_name, retrieved_address) = data_store.get_active_wallet()?;
    println!("   Retrieved: {retrieved_name} -> {retrieved_address}");

    // Verify they match
    if retrieved_name == active_name && retrieved_address == active_address {
        println!("   Values match original ✓");
    } else {
        println!("   ERROR: Values don't match!");
    }

    println!("\n5. Testing wallet switching:");

    // Switch to backup wallet
    let (backup_name, backup_address) = key_store.set_active_wallet("backup")?;
    data_store.set_active_wallet(&backup_name, &backup_address)?;
    println!("   Switched to backup wallet: {backup_name} -> {backup_address}");

    // Verify the switch
    let (current_name, _current_address) = data_store.get_active_wallet()?;
    if current_name == "backup" {
        println!("   Wallet switch successful ✓");
    } else {
        println!("   ERROR: Wallet switch failed!");
    }

    println!("\n6. Testing error handling:");

    // Try to set a non-existent wallet
    match key_store.set_active_wallet("nonexistent") {
        Ok(_) => println!("   ERROR: Should have failed for non-existent wallet!"),
        Err(e) => println!("   Correctly failed for non-existent wallet: {e}"),
    }

    println!("\n7. Testing persistence across DataStore instances:");

    // Create a new DataStore instance pointing to the same directory
    let new_data_store = DataStore::from_paths(
        data_dir.clone(),
        temp_dir.path().join("pods"),
        temp_dir.path().join("downloads"),
    )?;

    // Should be able to read the active wallet
    let (persistent_name, persistent_address) = new_data_store.get_active_wallet()?;
    println!("   New DataStore instance read: {persistent_name} -> {persistent_address}");

    if persistent_name == backup_name && persistent_address == backup_address {
        println!("   Persistence across instances works ✓");
    } else {
        println!("   ERROR: Persistence failed!");
    }

    println!("\nDemo completed successfully! 🎉");
    println!("\nThe active wallet functions correctly:");
    println!("  ✓ set_active_wallet() sets and persists the active wallet");
    println!("  ✓ get_active_wallet() retrieves the active wallet from storage");
    println!("  ✓ Data persists in active_wallet.json file");
    println!("  ✓ Error handling works for invalid wallets");
    println!("  ✓ Persistence works across application restarts");

    Ok(())
}
