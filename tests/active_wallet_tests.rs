mod common;
use colonylib::DataStore;
use common::create_test_datastore;
use std::fs;

#[test]
fn test_set_and_get_active_wallet() {
    let (datastore, _temp_dir) = create_test_datastore();

    let wallet_name = "test_wallet";
    let wallet_address = "******************************************";

    // Set active wallet
    datastore
        .set_active_wallet(wallet_name, wallet_address)
        .unwrap();

    // Get active wallet
    let (retrieved_name, retrieved_address) = datastore.get_active_wallet().unwrap();

    assert_eq!(retrieved_name, wallet_name);
    assert_eq!(retrieved_address, wallet_address);
}

#[test]
fn test_active_wallet_file_creation() {
    let (datastore, _temp_dir) = create_test_datastore();

    let wallet_name = "main_wallet";
    let wallet_address = "******************************************";

    // Set active wallet
    datastore
        .set_active_wallet(wallet_name, wallet_address)
        .unwrap();

    // Check that the active_wallet.json file was created
    let mut active_wallet_path = datastore.get_data_path();
    active_wallet_path.push("active_wallet.json");
    assert!(active_wallet_path.exists());

    // Verify the file contents
    let file_contents = fs::read_to_string(&active_wallet_path).unwrap();
    let json_value: serde_json::Value = serde_json::from_str(&file_contents).unwrap();

    assert_eq!(json_value["name"].as_str().unwrap(), wallet_name);
    assert_eq!(json_value["address"].as_str().unwrap(), wallet_address);
}

#[test]
fn test_get_active_wallet_file_not_found() {
    let (datastore, _temp_dir) = create_test_datastore();

    // Try to get active wallet when no file exists
    let result = datastore.get_active_wallet();
    assert!(result.is_err());

    // Verify it's specifically a "not found" error
    let error = result.unwrap_err();
    assert!(error.to_string().contains("Active wallet file not found"));
}

#[test]
fn test_active_wallet_overwrite() {
    let (datastore, _temp_dir) = create_test_datastore();

    // Set first wallet
    let first_name = "wallet1";
    let first_address = "******************************************";
    datastore
        .set_active_wallet(first_name, first_address)
        .unwrap();

    // Verify first wallet is set
    let (name, address) = datastore.get_active_wallet().unwrap();
    assert_eq!(name, first_name);
    assert_eq!(address, first_address);

    // Set second wallet (should overwrite)
    let second_name = "wallet2";
    let second_address = "******************************************";
    datastore
        .set_active_wallet(second_name, second_address)
        .unwrap();

    // Verify second wallet is now active
    let (name, address) = datastore.get_active_wallet().unwrap();
    assert_eq!(name, second_name);
    assert_eq!(address, second_address);
}

#[test]
fn test_active_wallet_json_format() {
    let (datastore, _temp_dir) = create_test_datastore();

    let wallet_name = "format_test";
    let wallet_address = "******************************************";

    // Set active wallet
    datastore
        .set_active_wallet(wallet_name, wallet_address)
        .unwrap();

    // Read the file directly and verify JSON structure
    let mut active_wallet_path = datastore.get_data_path();
    active_wallet_path.push("active_wallet.json");

    let file_contents = fs::read_to_string(&active_wallet_path).unwrap();
    let json_value: serde_json::Value = serde_json::from_str(&file_contents).unwrap();

    // Verify JSON structure
    assert!(json_value.is_object());
    assert!(json_value.get("name").is_some());
    assert!(json_value.get("address").is_some());
    assert_eq!(json_value.as_object().unwrap().len(), 2); // Only name and address fields
}

#[test]
fn test_active_wallet_empty_values() {
    let (datastore, _temp_dir) = create_test_datastore();

    // Test with empty name and address
    let empty_name = "";
    let empty_address = "";

    datastore
        .set_active_wallet(empty_name, empty_address)
        .unwrap();

    let (retrieved_name, retrieved_address) = datastore.get_active_wallet().unwrap();
    assert_eq!(retrieved_name, empty_name);
    assert_eq!(retrieved_address, empty_address);
}

#[test]
fn test_active_wallet_special_characters() {
    let (datastore, _temp_dir) = create_test_datastore();

    // Test with special characters in name
    let special_name = "wallet-with_special.chars@123";
    let address = "******************************************";

    datastore.set_active_wallet(special_name, address).unwrap();

    let (retrieved_name, retrieved_address) = datastore.get_active_wallet().unwrap();
    assert_eq!(retrieved_name, special_name);
    assert_eq!(retrieved_address, address);
}

#[test]
fn test_active_wallet_persistence() {
    let (datastore, temp_dir) = create_test_datastore();

    let wallet_name = "persistent_wallet";
    let wallet_address = "******************************************";

    // Set active wallet
    datastore
        .set_active_wallet(wallet_name, wallet_address)
        .unwrap();

    // Create a new DataStore instance pointing to the same directory
    let data_dir = temp_dir.path().join("data");
    let pods_dir = temp_dir.path().join("pods");
    let downloads_dir = temp_dir.path().join("downloads");

    let new_datastore = DataStore::from_paths(data_dir, pods_dir, downloads_dir).unwrap();

    // Verify the active wallet persists across DataStore instances
    let (retrieved_name, retrieved_address) = new_datastore.get_active_wallet().unwrap();
    assert_eq!(retrieved_name, wallet_name);
    assert_eq!(retrieved_address, wallet_address);
}
